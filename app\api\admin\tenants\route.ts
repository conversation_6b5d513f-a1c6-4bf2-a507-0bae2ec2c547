import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'
import { createTenant } from '@/lib/tenant'
import { z } from 'zod'

const createTenantSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  slug: z.string().min(1, 'Slug is required').regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
  domain: z.string().optional(),
  adminEmail: z.string().email('Invalid admin email'),
  adminName: z.string().min(1, 'Admin name is required'),
  adminPassword: z.string().min(6, 'Admin password must be at least 6 characters'),
  maxUsers: z.number().optional(),
  maxStorage: z.number().optional(),
  plan: z.enum(['basic', 'premium', 'enterprise']).default('basic'),
  billingEmail: z.string().email().optional(),
})

const updateTenantSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  domain: z.string().optional(),
  logo: z.string().optional(),
  settings: z.record(z.any()).optional(),
  isActive: z.boolean().optional(),
  maxUsers: z.number().optional(),
  maxStorage: z.number().optional(),
  plan: z.enum(['basic', 'premium', 'enterprise']).optional(),
  billingEmail: z.string().email().optional(),
  subscriptionStatus: z.enum(['active', 'suspended', 'cancelled']).optional(),
})

// Check if user is super admin
async function checkSuperAdmin(session: any) {
  if (!session?.user?.id) {
    return false
  }

  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { isSuperAdmin: true }
  })

  return user?.isSuperAdmin || false
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const isSuperAdmin = await checkSuperAdmin(session)
    if (!isSuperAdmin) {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || 'all'

    const skip = (page - 1) * limit

    const where: any = {}
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { slug: { contains: search, mode: 'insensitive' } },
        { domain: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (status !== 'all') {
      where.isActive = status === 'active'
    }

    const [tenants, total] = await Promise.all([
      prisma.tenant.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          _count: {
            select: {
              users: true,
              customers: true,
              orders: true,
            }
          }
        }
      }),
      prisma.tenant.count({ where })
    ])

    return NextResponse.json({
      tenants,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching tenants:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const isSuperAdmin = await checkSuperAdmin(session)
    if (!isSuperAdmin) {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = createTenantSchema.parse(body)

    // Check if slug already exists
    const existingTenant = await prisma.tenant.findUnique({
      where: { slug: validatedData.slug }
    })

    if (existingTenant) {
      return NextResponse.json({ error: 'Tenant with this slug already exists' }, { status: 400 })
    }

    // Check if domain already exists (if provided)
    if (validatedData.domain) {
      const existingDomain = await prisma.tenant.findUnique({
        where: { domain: validatedData.domain }
      })

      if (existingDomain) {
        return NextResponse.json({ error: 'Tenant with this domain already exists' }, { status: 400 })
      }
    }

    const tenant = await createTenant({
      name: validatedData.name,
      slug: validatedData.slug,
      domain: validatedData.domain,
      adminEmail: validatedData.adminEmail,
      adminName: validatedData.adminName,
      adminPassword: validatedData.adminPassword,
    })

    // Update tenant with additional settings if provided
    if (validatedData.maxUsers || validatedData.maxStorage || validatedData.plan || validatedData.billingEmail) {
      await prisma.tenant.update({
        where: { id: tenant.id },
        data: {
          maxUsers: validatedData.maxUsers,
          maxStorage: validatedData.maxStorage,
          plan: validatedData.plan,
          billingEmail: validatedData.billingEmail,
        }
      })
    }

    const updatedTenant = await prisma.tenant.findUnique({
      where: { id: tenant.id },
      include: {
        _count: {
          select: {
            users: true,
            customers: true,
            orders: true,
          }
        }
      }
    })

    return NextResponse.json(updatedTenant, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 })
    }
    
    console.error('Error creating tenant:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
