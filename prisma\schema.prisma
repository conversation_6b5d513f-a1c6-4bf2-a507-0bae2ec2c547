// Multi-Tenant ERP Prisma Schema
// This schema implements a shared database, separate schema multi-tenancy pattern

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// TENANT MANAGEMENT
// ============================================================================

model Tenant {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique // URL-friendly identifier
  domain      String?  @unique // Custom domain (optional)
  logo        String?
  settings    Json?    // Tenant-specific settings
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  users       User[]
  roles       Role[]
  permissions Permission[]

  // ERP Modules
  customers   Customer[]
  suppliers   Supplier[]
  products    Product[]
  categories  Category[]
  orders      Order[]
  invoices    Invoice[]
  payments    Payment[]
  inventory   InventoryItem[]

  @@map("tenants")
}

// ============================================================================
// USER MANAGEMENT & AUTHENTICATION
// ============================================================================

model User {
  id            String    @id @default(cuid())
  tenantId      String
  email         String
  emailVerified DateTime?
  name          String?
  image         String?
  password      String?   // Hashed password for credentials auth
  isActive      Boolean   @default(true)
  lastLoginAt   DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  tenant        Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  accounts      Account[]
  sessions      Session[]
  userRoles     UserRole[]

  // ERP Relations
  createdOrders Order[]   @relation("OrderCreatedBy")
  createdInvoices Invoice[] @relation("InvoiceCreatedBy")
  createdPayments Payment[] @relation("PaymentCreatedBy")

  @@unique([tenantId, email]) // Email unique per tenant
  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// ============================================================================
// ROLE-BASED ACCESS CONTROL (RBAC)
// ============================================================================

model Role {
  id          String   @id @default(cuid())
  tenantId    String
  name        String   // e.g., "Admin", "Manager", "Employee", "Viewer"
  description String?
  isSystem    Boolean  @default(false) // System roles cannot be deleted
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant        Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  userRoles     UserRole[]
  rolePermissions RolePermission[]

  @@unique([tenantId, name])
  @@map("roles")
}

model Permission {
  id          String   @id @default(cuid())
  tenantId    String
  name        String   // e.g., "users.create", "orders.read", "invoices.delete"
  resource    String   // e.g., "users", "orders", "invoices"
  action      String   // e.g., "create", "read", "update", "delete"
  description String?
  createdAt   DateTime @default(now())

  // Relations
  tenant          Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  rolePermissions RolePermission[]

  @@unique([tenantId, name])
  @@map("permissions")
}

model UserRole {
  id       String @id @default(cuid())
  userId   String
  roleId   String
  assignedAt DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("user_roles")
}

model RolePermission {
  id           String @id @default(cuid())
  roleId       String
  permissionId String
  grantedAt    DateTime @default(now())

  // Relations
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@map("role_permissions")
}

// ============================================================================
// ERP CORE MODULES
// ============================================================================

// Customer Management
model Customer {
  id          String   @id @default(cuid())
  tenantId    String
  name        String
  email       String?
  phone       String?
  address     String?
  city        String?
  state       String?
  zipCode     String?
  country     String?
  taxId       String?  // Tax identification number
  isActive    Boolean  @default(true)
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant   Tenant  @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  orders   Order[]
  invoices Invoice[]

  @@unique([tenantId, email])
  @@map("customers")
}

// Supplier Management
model Supplier {
  id          String   @id @default(cuid())
  tenantId    String
  name        String
  email       String?
  phone       String?
  address     String?
  city        String?
  state       String?
  zipCode     String?
  country     String?
  taxId       String?
  isActive    Boolean  @default(true)
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant   Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  products Product[]

  @@unique([tenantId, email])
  @@map("suppliers")
}

// Product Catalog
model Category {
  id          String    @id @default(cuid())
  tenantId    String
  name        String
  description String?
  parentId    String?   // For hierarchical categories
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  tenant   Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  parent   Category? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")
  products Product[]

  @@unique([tenantId, name])
  @@map("categories")
}

model Product {
  id          String   @id @default(cuid())
  tenantId    String
  sku         String   // Stock Keeping Unit
  name        String
  description String?
  categoryId  String?
  supplierId  String?
  unitPrice   Decimal  @db.Decimal(10, 2)
  costPrice   Decimal? @db.Decimal(10, 2)
  unit        String   // e.g., "piece", "kg", "liter"
  minStock    Int?     // Minimum stock level
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant       Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  category     Category?     @relation(fields: [categoryId], references: [id])
  supplier     Supplier?     @relation(fields: [supplierId], references: [id])
  orderItems   OrderItem[]
  invoiceItems InvoiceItem[]
  inventory    InventoryItem[]

  @@unique([tenantId, sku])
  @@map("products")
}

// Inventory Management
model InventoryItem {
  id          String   @id @default(cuid())
  tenantId    String
  productId   String
  quantity    Int
  location    String?  // Warehouse location
  lastUpdated DateTime @default(now())

  // Relations
  tenant  Tenant  @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([tenantId, productId, location])
  @@map("inventory_items")
}

// Order Management
enum OrderStatus {
  DRAFT
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  RETURNED
}

model Order {
  id          String      @id @default(cuid())
  tenantId    String
  orderNumber String      // Human-readable order number
  customerId  String
  status      OrderStatus @default(DRAFT)
  orderDate   DateTime    @default(now())
  dueDate     DateTime?
  subtotal    Decimal     @db.Decimal(10, 2)
  taxAmount   Decimal     @db.Decimal(10, 2) @default(0)
  totalAmount Decimal     @db.Decimal(10, 2)
  notes       String?
  createdById String
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  tenant      Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  customer    Customer    @relation(fields: [customerId], references: [id])
  createdBy   User        @relation("OrderCreatedBy", fields: [createdById], references: [id])
  orderItems  OrderItem[]
  invoices    Invoice[]

  @@unique([tenantId, orderNumber])
  @@map("orders")
}

model OrderItem {
  id        String  @id @default(cuid())
  orderId   String
  productId String
  quantity  Int
  unitPrice Decimal @db.Decimal(10, 2)
  total     Decimal @db.Decimal(10, 2)

  // Relations
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

// Invoice Management
enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
  CANCELLED
}

model Invoice {
  id            String        @id @default(cuid())
  tenantId      String
  invoiceNumber String        // Human-readable invoice number
  customerId    String
  orderId       String?       // Optional link to order
  status        InvoiceStatus @default(DRAFT)
  issueDate     DateTime      @default(now())
  dueDate       DateTime
  subtotal      Decimal       @db.Decimal(10, 2)
  taxAmount     Decimal       @db.Decimal(10, 2) @default(0)
  totalAmount   Decimal       @db.Decimal(10, 2)
  paidAmount    Decimal       @db.Decimal(10, 2) @default(0)
  notes         String?
  createdById   String
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  tenant       Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  customer     Customer      @relation(fields: [customerId], references: [id])
  order        Order?        @relation(fields: [orderId], references: [id])
  createdBy    User          @relation("InvoiceCreatedBy", fields: [createdById], references: [id])
  invoiceItems InvoiceItem[]
  payments     Payment[]

  @@unique([tenantId, invoiceNumber])
  @@map("invoices")
}

model InvoiceItem {
  id        String  @id @default(cuid())
  invoiceId String
  productId String
  quantity  Int
  unitPrice Decimal @db.Decimal(10, 2)
  total     Decimal @db.Decimal(10, 2)

  // Relations
  invoice Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("invoice_items")
}

// Payment Management
enum PaymentMethod {
  CASH
  CREDIT_CARD
  DEBIT_CARD
  BANK_TRANSFER
  CHECK
  PAYPAL
  STRIPE
  OTHER
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
}

model Payment {
  id              String        @id @default(cuid())
  tenantId        String
  invoiceId       String
  amount          Decimal       @db.Decimal(10, 2)
  method          PaymentMethod
  status          PaymentStatus @default(PENDING)
  transactionId   String?       // External transaction ID
  paymentDate     DateTime      @default(now())
  notes           String?
  createdById     String
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  tenant    Tenant  @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  invoice   Invoice @relation(fields: [invoiceId], references: [id])
  createdBy User    @relation("PaymentCreatedBy", fields: [createdById], references: [id])

  @@map("payments")
}
