import NextAuth from "next-auth"
import Credentials from "next-auth/providers/credentials"
import { z } from "zod"
import bcrypt from "bcryptjs"
import { prisma } from "./lib/prisma"
import { MultiTenantPrismaAdapter } from "./lib/auth-adapter"
import { getTenantBySlug } from "./lib/tenant"

const signinSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  tenantSlug: z.string().optional(), // Optional tenant slug for multi-tenant login
})

export const { handlers, auth, signIn, signOut } = NextAuth({
  adapter: MultiTenantPrismaAdapter({
    async getTenantId(_email: string) {
      // Default tenant resolution logic
      // You can customize this based on your requirements
      const defaultTenant = await getTenantBySlug(process.env.DEFAULT_TENANT_SLUG || 'default')
      return defaultTenant?.id || null
    }
  }),
  providers: [
    Credentials({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
        tenantSlug: { label: "Organization", type: "text" },
      },
      async authorize(credentials) {
        try {
          if (!credentials?.email || !credentials?.password) {
            return null
          }

          const { email, password, tenantSlug } = signinSchema.parse(credentials)

          // Find tenant
          let tenant = null
          if (tenantSlug) {
            tenant = await getTenantBySlug(tenantSlug)
          } else {
            // Use default tenant if no slug provided
            tenant = await getTenantBySlug(process.env.DEFAULT_TENANT_SLUG || 'default')
          }

          if (!tenant) {
            console.error("Tenant not found:", tenantSlug)
            return null
          }

          // First check for super admin user (not tied to any tenant)
          let user = await prisma.user.findUnique({
            where: {
              email: email,
              isSuperAdmin: true,
              tenantId: null
            },
            include: {
              tenant: true,
              userRoles: {
                include: {
                  role: {
                    include: {
                      rolePermissions: {
                        include: {
                          permission: true
                        }
                      }
                    }
                  }
                }
              }
            }
          })

          // If not super admin, find user in the specific tenant
          if (!user) {
            user = await prisma.user.findUnique({
              where: {
                tenantId_email: {
                  tenantId: tenant.id,
                  email: email
                }
              },
              include: {
                tenant: true,
                userRoles: {
                  include: {
                    role: {
                      include: {
                        rolePermissions: {
                          include: {
                            permission: true
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          }

          if (!user || !user.password) {
            console.error("User not found or no password set")
            return null
          }

          // Verify password
          const isValidPassword = await bcrypt.compare(password, user.password)
          if (!isValidPassword) {
            console.error("Invalid password")
            return null
          }

          // Update last login
          await prisma.user.update({
            where: { id: user.id },
            data: { lastLoginAt: new Date() }
          })

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image,
            tenantId: user.tenantId,
            tenantSlug: user.tenant?.slug || null,
            isSuperAdmin: user.isSuperAdmin || false,
          }
        } catch (error) {
          console.error("Authentication error:", error)
          return null
        }
      },
    }),
  ],
  pages: {
    signIn: "/signin",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id
        token.tenantId = (user as any).tenantId
        token.tenantSlug = (user as any).tenantSlug
        token.isSuperAdmin = (user as any).isSuperAdmin
      }
      return token
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.id as string
        ;(session as any).tenantId = token.tenantId
        ;(session as any).tenantSlug = token.tenantSlug
        ;(session as any).isSuperAdmin = token.isSuperAdmin
      }
      return session
    },
  },
  session: {
    strategy: "jwt",
  },
})
