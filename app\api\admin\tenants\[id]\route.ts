import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateTenantSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  domain: z.string().optional(),
  logo: z.string().optional(),
  settings: z.record(z.any()).optional(),
  isActive: z.boolean().optional(),
  maxUsers: z.number().optional(),
  maxStorage: z.number().optional(),
  plan: z.enum(['basic', 'premium', 'enterprise']).optional(),
  billingEmail: z.string().email().optional(),
  subscriptionStatus: z.enum(['active', 'suspended', 'cancelled']).optional(),
})

// Check if user is super admin
async function checkSuperAdmin(session: any) {
  if (!session?.user?.id) {
    return false
  }

  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { isSuperAdmin: true }
  })

  return user?.isSuperAdmin || false
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const isSuperAdmin = await checkSuperAdmin(session)
    if (!isSuperAdmin) {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const tenant = await prisma.tenant.findUnique({
      where: { id: params.id },
      include: {
        users: {
          select: {
            id: true,
            email: true,
            name: true,
            isActive: true,
            lastLoginAt: true,
            createdAt: true,
            userRoles: {
              include: {
                role: {
                  select: {
                    name: true,
                    description: true
                  }
                }
              }
            }
          }
        },
        _count: {
          select: {
            users: true,
            customers: true,
            suppliers: true,
            products: true,
            orders: true,
            invoices: true,
            payments: true,
          }
        }
      }
    })

    if (!tenant) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 404 })
    }

    return NextResponse.json(tenant)
  } catch (error) {
    console.error('Error fetching tenant:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const isSuperAdmin = await checkSuperAdmin(session)
    if (!isSuperAdmin) {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = updateTenantSchema.parse(body)

    // Check if tenant exists
    const existingTenant = await prisma.tenant.findUnique({
      where: { id: params.id }
    })

    if (!existingTenant) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 404 })
    }

    // Check if domain already exists (if being updated)
    if (validatedData.domain && validatedData.domain !== existingTenant.domain) {
      const existingDomain = await prisma.tenant.findUnique({
        where: { domain: validatedData.domain }
      })

      if (existingDomain) {
        return NextResponse.json({ error: 'Tenant with this domain already exists' }, { status: 400 })
      }
    }

    const updatedTenant = await prisma.tenant.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        _count: {
          select: {
            users: true,
            customers: true,
            suppliers: true,
            products: true,
            orders: true,
            invoices: true,
            payments: true,
          }
        }
      }
    })

    return NextResponse.json(updatedTenant)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 })
    }
    
    console.error('Error updating tenant:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const isSuperAdmin = await checkSuperAdmin(session)
    if (!isSuperAdmin) {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    // Check if tenant exists
    const existingTenant = await prisma.tenant.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            users: true,
            customers: true,
            orders: true,
          }
        }
      }
    })

    if (!existingTenant) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 404 })
    }

    // Prevent deletion if tenant has data
    const hasData = existingTenant._count.users > 0 || 
                   existingTenant._count.customers > 0 || 
                   existingTenant._count.orders > 0

    if (hasData) {
      return NextResponse.json({ 
        error: 'Cannot delete tenant with existing data. Please deactivate instead.' 
      }, { status: 400 })
    }

    await prisma.tenant.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Tenant deleted successfully' })
  } catch (error) {
    console.error('Error deleting tenant:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
